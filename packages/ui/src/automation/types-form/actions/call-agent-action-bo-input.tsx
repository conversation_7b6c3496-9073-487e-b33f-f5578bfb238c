import type { ILocaleContext } from '@bika/contents/i18n';
import type { CallAgentAction } from '@bika/types/automation/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import { useSpaceContext } from '@bika/types/space/context';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { ToSchemaBOInput } from '@bika/ui/unit/types-form/to-schema-bo-input';
import { FormLabel, FormHelperText } from '../../../form-components';
import { NodeResourceSelect } from '../../../node/types-form/node-resource-select';

interface CallAgentActionBOInputProps {
  value: CallAgentAction;
  onChange: (newVal: CallAgentAction) => void;
  locale: ILocaleContext;
}

export const CallAgentActionBOInput = (props: CallAgentActionBOInputProps) => {
  const { value, locale } = props;
  const api = useNodeResourceApiContext();
  const { t } = locale;
  const space = useSpaceContext();
  return (
    <>
      <NodeResourceSelect
        resourceType="AI"
        resourceId={value.input.agentId}
        required
        setResourceId={(newVal) => {
          props.onChange({
            ...value,
            input: {
              ...value.input,
              agentId: newVal,
            },
          });
        }}
        locale={locale}
        label={t.automation.action.call_agent.agent_id_subject}
      />

      <FormLabel required={true}>{t.automation.action.call_agent.recipient}</FormLabel>

      <FormHelperText sx={{ mb: 1 }}>{t.tips.call_agent_recipient_helper}</FormHelperText>

      <ToSchemaBOInput
        types={['UNIT_MEMBER']}
        // label={t.automation.action.call_agent.recipient}
        targets={['Member']}
        value={
          value.input.to?.[0] ?? {
            type: 'UNIT_MEMBER',
            memberId: space?.myInfo?.id ?? '',
          }
        }
        multiple={false}
        onChange={(newVal) => {
          props.onChange({
            ...value,
            input: {
              ...value.input,
              to: [newVal],
            },
          });
        }}
        locale={locale}
        api={api}
      />

      <VariablesTextInput
        locale={locale}
        label={t.automation.action.call_agent.message_subject}
        value={value.input.message}
        required
        onChange={(newVal) => {
          props.onChange({
            ...value,
            input: {
              ...value.input,
              message: newVal,
            },
          });
        }}
      />
    </>
  );
};
