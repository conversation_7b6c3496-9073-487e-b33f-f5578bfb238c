'use client';

import { useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { INodeResourceApi } from '@bika/types/node/context';
import type { ToSpecifyUnits } from '@bika/types/unit/bo';
import { IconButton } from '@bika/ui/button-component';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import SettingOutlined from '@bika/ui/icons/components/setting_outlined';
import { Box } from '@bika/ui/layout-components';
import { Dropdown, MenuButton, Menu, MenuItem } from '@bika/ui/menu';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { MemberBoSelect } from './member-bo-select';
import type { SelectType } from './member-select-modal';

interface MemberVariableSelectProps {
  locale: ILocaleContext;
  value: ToSpecifyUnits;
  targets?: SelectType[];
  multiple?: boolean;
  onChange: (newVal: ToSpecifyUnits) => void;
  api: INodeResourceApi;
  actionId?: string;
}

export const MemberVariableSelect = (props: MemberVariableSelectProps) => {
  const { value, onChange, api, locale, actionId, multiple, targets } = props;
  const { t } = locale;

  const unitIds = value.unitIds || [];

  const variables = api.automation.getAutomationGlobalVariables(actionId);

  const [isPrimary, setIsPrimary] = useState(() => {
    if (typeof unitIds[0] === 'string' && unitIds[0].includes('<%')) {
      return false;
    }
    return true;
  });

  return (
    <Box display="flex" justifyContent="space-between" alignItems={isPrimary ? 'center' : 'flex-start'}>
      {!isPrimary ? (
        <VariablesTextInput
          automationVariables={variables}
          value={unitIds[0]}
          locale={locale}
          sx={{
            minHeight: '40px',
            width: '100%',
          }}
          onChange={(newVal) => {
            onChange({
              ...value,
              unitIds: [newVal!],
            });
          }}
        />
      ) : (
        <MemberBoSelect
          multiple={multiple}
          locale={locale}
          targets={targets}
          value={value}
          onChange={props.onChange}
          api={api}
        />
      )}
      <Dropdown>
        <MenuButton
          slots={{ root: IconButton }}
          slotProps={{ root: { variant: 'outlined', color: 'neutral' } }}
          sx={{
            ml: 1,
            width: '30px',
          }}
        >
          <SettingOutlined />
        </MenuButton>
        <Menu placement="bottom-end">
          <MenuItem
            aria-checked={isPrimary}
            onClick={() => {
              setIsPrimary(true);
            }}
          >
            {t.automation.variable_select.primary_component}
            {isPrimary && <CheckOutlined />}
          </MenuItem>
          <MenuItem
            aria-checked={!isPrimary}
            onClick={() => {
              setIsPrimary(false);
            }}
          >
            {t.automation.variable_select.variable_component}
            {!isPrimary && <CheckOutlined />}
          </MenuItem>
        </Menu>
      </Dropdown>
    </Box>
  );
};
